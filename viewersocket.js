require('dotenv').config();
const WebSocket = require('ws');
const EventEmitter = require('events');

class ViewerSocketClient extends EventEmitter {
    constructor() {
        super();
        this.socket = null;
        this.viewers = new Set();
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 5000; // 5 seconds
    }

    connect() {
        return new Promise((resolve, reject) => {
            // Replace <TOKEN> with the actual token from environment
            const wsUrl = process.env.PUMP_SECOND_WS.replace('<TOKEN>', process.env.PUMP_SECOND_WS_COOKIE);
            
            console.log('Connecting to LiveKit viewer socket...');
            
            this.socket = new WebSocket(wsUrl, {
                headers: {
                    'Origin': 'https://pump.fun',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Cookie': `access_token=${process.env.PUMP_SECOND_WS_COOKIE}`
                }
            });

            this.socket.on('open', () => {
                console.log('Connected to LiveKit viewer socket');
                this.reconnectAttempts = 0;
                this.emit('connected');
                resolve();
            });

            this.socket.on('error', (error) => {
                console.error('LiveKit viewer socket error:', error);
                this.emit('error', error);
                reject(error);
            });

            this.socket.on('close', (code, reason) => {
                console.log(`LiveKit viewer socket closed: ${code} - ${reason}`);
                this.emit('disconnected', { code, reason });
                this.handleReconnect();
            });

            this.socket.on('message', (data) => {
                this.handleMessage(data);
            });
        });
    }

    handleMessage(data) {
        try {
            // LiveKit sends binary data, but we need to check if it contains JSON
            let message;
            
            if (Buffer.isBuffer(data)) {
                // Try to parse as text first
                const textData = data.toString('utf8');
                
                // Check if it looks like JSON
                if (textData.trim().startsWith('{') || textData.trim().startsWith('[')) {
                    try {
                        message = JSON.parse(textData);
                        this.processJsonMessage(message);
                        return;
                    } catch (e) {
                        // Not JSON, handle as binary
                    }
                }
                
                // Handle binary data - look for JSON patterns within binary
                this.processBinaryMessage(data);
            } else {
                // Handle text data
                try {
                    message = JSON.parse(data);
                    this.processJsonMessage(message);
                } catch (e) {
                    console.log('Received non-JSON text message:', data.toString());
                }
            }
        } catch (error) {
            console.error('Error handling message:', error);
        }
    }

    processBinaryMessage(data) {
        // Convert binary to string and look for JSON patterns
        const textData = data.toString('utf8');
        
        // Look for JSON objects within the binary data
        const jsonMatches = textData.match(/\{[^{}]*"role"[^{}]*\}/g);
        
        if (jsonMatches) {
            jsonMatches.forEach(jsonStr => {
                try {
                    const jsonObj = JSON.parse(jsonStr);
                    this.processJsonMessage(jsonObj);
                } catch (e) {
                    // Ignore invalid JSON
                }
            });
        }
        
        // Emit raw binary data for other processing
        this.emit('binaryMessage', data);
    }

    processJsonMessage(message) {
        console.log('Received JSON message:', message);
        
        // Handle different message types based on LiveKit protocol
        if (message.role) {
            this.handleRoleMessage(message);
        }
        
        if (message.type) {
            this.handleTypedMessage(message);
        }
        
        // Emit the parsed message for external handling
        this.emit('jsonMessage', message);
    }

    handleRoleMessage(message) {
        if (message.role === 'viewer') {
            console.log('Viewer role message received:', message);
            
            // Track viewer information
            if (message.userId || message.id) {
                const viewerId = message.userId || message.id;
                this.viewers.add(viewerId);
                
                this.emit('viewerJoined', {
                    viewerId,
                    role: message.role,
                    metadata: message
                });
            }
        }
    }

    handleTypedMessage(message) {
        switch (message.type) {
            case 'participant_joined':
                this.handleParticipantJoined(message);
                break;
            case 'participant_left':
                this.handleParticipantLeft(message);
                break;
            case 'data_received':
                this.handleDataReceived(message);
                break;
            default:
                console.log('Unknown message type:', message.type, message);
        }
    }

    handleParticipantJoined(message) {
        console.log('Participant joined:', message);
        this.emit('participantJoined', message);
    }

    handleParticipantLeft(message) {
        console.log('Participant left:', message);
        if (message.participantId) {
            this.viewers.delete(message.participantId);
        }
        this.emit('participantLeft', message);
    }

    handleDataReceived(message) {
        console.log('Data received:', message);
        this.emit('dataReceived', message);
    }

    handleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${this.reconnectDelay}ms...`);
            
            setTimeout(() => {
                this.connect().catch(error => {
                    console.error('Reconnection failed:', error);
                });
            }, this.reconnectDelay);
        } else {
            console.error('Max reconnection attempts reached. Giving up.');
            this.emit('maxReconnectAttemptsReached');
        }
    }

    getViewers() {
        return Array.from(this.viewers);
    }

    getViewerCount() {
        return this.viewers.size;
    }

    sendMessage(message) {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            if (typeof message === 'object') {
                this.socket.send(JSON.stringify(message));
            } else {
                this.socket.send(message);
            }
        } else {
            console.warn('Cannot send message: socket not connected');
        }
    }

    disconnect() {
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
        this.viewers.clear();
        this.reconnectAttempts = 0;
    }
}

module.exports = ViewerSocketClient;
