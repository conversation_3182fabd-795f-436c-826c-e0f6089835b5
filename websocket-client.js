require('dotenv').config();
const WebSocket = require('ws');
const EventEmitter = require('events');

class PumpFunWebSocketClient extends EventEmitter {
    constructor(roomId) {
        super();
        this.roomId = roomId;
        this.socket = null;
        this.participants = new Set();
        this.chatMessages = [];
        this.validMoves = ['up', 'down', 'left', 'right', 'a', 'b', 'start', 'select'];
    }

    connect() {
        return new Promise((resolve, reject) => {
            this.socket = new WebSocket(process.env.PUMP_WS, {
                headers: {
                    'Origin': 'https://pump.fun',
                    'User-Agent': 'Mozilla/5.0'
                }
            });

            this.socket.on('open', () => {
                console.log('Connected to PumpFun websocket');
                resolve();
            });

            this.socket.on('error', (error) => {
                console.error('Connection error:', error);
                reject(error);
            });

            this.socket.on('close', () => {
                console.log('Disconnected from PumpFun websocket');
            });

            this.socket.on('message', (data) => {
                this.handleMessage(data.toString());
            });
        });
    }

    handleMessage(message) {
        // Handle ping/pong
        if (message === '2') {
            this.socket.send('3');
            return;
        }

        // Initial handshake
        if (message.startsWith('0')) {
            const connectPayload = {
                origin: 'https://pump.fun',
                timestamp: Date.now(),
                token: null
            };
            this.socket.send('40' + JSON.stringify(connectPayload));
            return;
        }

        // Join room after handshake
        if (message.startsWith('40')) {
            this.joinRoom();
            return;
        }

        // Process Socket.IO messages
        if (message.startsWith('42')) {
            try {
                const data = JSON.parse(message.substring(2));
                this.processSocketMessage(data);
            } catch (error) {
                console.error('Parse error:', error);
            }
        }
    }

    joinRoom() {
        if (this.socket && this.roomId) {
            const joinPayload = [
                'joinRoom',
                { roomId: this.roomId, username: '' }
            ];
            this.socket.send('42' + JSON.stringify(joinPayload));
            console.log(`Joined room: ${this.roomId}`);
        }
    }

    processSocketMessage(data) {
        if (!Array.isArray(data) || data.length < 1) {
            console.log('Invalid data:', data);
            return;
        };

        const [eventType, eventData] = data;
        console.log(eventType, eventData)
        switch (eventType) {
            case 'userJoined':
                if (eventData?.userId || eventData?.userAddress) {
                    const userId = eventData.userId || eventData.userAddress;
                    this.participants.add(userId);
                    console.log(`User joined: ${eventData.username || userId}`);
                    this.emit('userJoined', eventData);
                }
                break;

            case 'userLeft':
                if (eventData?.userId || eventData?.userAddress) {
                    const userId = eventData.userId || eventData.userAddress;
                    this.participants.delete(userId);
                    console.log(`User left: ${eventData.username || userId}`);
                    this.emit('userLeft', eventData);
                }
                break;

            case 'newMessage':
            case 'message':
                this.chatMessages.push({
                    ...eventData,
                    timestamp: Date.now()
                });
                this.handleChatMessage(eventData);
                break;

            case 'setCookie':
                // Ignore cookie events
                break;

            default:
                console.log('Unknown event:', eventType, eventData);
        }
    }

    handleChatMessage(data) {
        const message = data.message?.toLowerCase().trim();
        const userId = data.userAddress || data.userId || data.username;

        if (userId && message && this.validMoves.includes(message)) {
            console.log(`Valid move "${message}" from player: ${data.username || userId.substring(0, 8)}`);
            this.emit('validMove', {
                userId,
                username: data.username,
                message,
                timestamp: Date.now()
            });
        }

        // Emit all chat messages for potential other uses
        this.emit('chatMessage', data);
    }

    getParticipants() {
        return Array.from(this.participants);
    }

    getChatMessages() {
        return this.chatMessages;
    }

    disconnect() {
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
    }
}

module.exports = PumpFunWebSocketClient;